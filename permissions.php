<?php
/**
 * Subscription Permissions
 *
 * This file defines the permissions available for each subscription level.
 * Instead of using a database, we define permissions as arrays for each subscription level.
 */

// Include StripeLogger for better logging
require_once 'stripe/StripeLogger.php';

/**
 * Get permissions for a specific subscription level
 *
 * @param string $level Subscription level (free, basic, advance, premium)
 * @return array Array of permission strings
 */
function getPermissionsByLevel($level) {
    // Use static variables to ensure they're only defined once and persist between function calls
    static $FREE_PERMISSIONS = null;
    static $BASIC_PERMISSIONS = null;
    static $ADVANCE_PERMISSIONS = null;
    static $PREMIUM_PERMISSIONS = null;
    static $initialized = false;

    // Ensure case-insensitive matching by converting to lowercase
    $level = strtolower($level);

    // Initialize permission arrays only once
    if (!$initialized) {
        StripeLogger::log(StripeLogLevel::INFO, "Initializing permission arrays for the first time");

        // Free plan permissions (available to all users)
        $FREE_PERMISSIONS = [
            'can_view_coin_details',
            'can_view_ido_details',
            'can_view_watchlists',
            'can_add_1_watchlist',
            'can_view_alerts'
        ];

        // Basic plan permissions (includes all free permissions plus additional ones)
        $BASIC_PERMISSIONS = array_merge($FREE_PERMISSIONS, [
            'can_perform_basic_search',
            'can_add_3_watchlists',
            'can_edit_watchlists',
            'can_delete_watchlists',
            'can_add_alerts',
            'can_add_5_alerts',
            'can_delete_alerts'
        ]);

        // Advance plan permissions (includes all basic permissions plus additional ones)
        $ADVANCE_PERMISSIONS = array_merge($BASIC_PERMISSIONS, [
            'can_add_10_watchlists',
            'can_perform_advanced_search',
            'can_add_20_alerts',
            'can_add_ido_watchlists',
            'can_edit_ido_watchlists',
            'can_delete_ido_watchlists',
            'can_view_advanced_coin_details'
        ]);

        // Premium plan permissions (all features)
        $PREMIUM_PERMISSIONS = array_merge($ADVANCE_PERMISSIONS, [
            'can_add_unlimited_watchlists',
            'can_add_100_alerts', // High limit for premium users
            // 'can_add_unlimited_alerts', // Commented out - can be enabled in future if needed
            'can_access_api',
            'can_access_advanced_analytics',
            'can_track_portfolio',
            'can_get_priority_support'
        ]);

        $initialized = true;
    }

    // Add debug logging with StripeLogger
    StripeLogger::log(StripeLogLevel::DEBUG, "getPermissionsByLevel called with level: " . $level, [
        'level' => $level,
        'initialized' => $initialized
    ]);

    // Log the permissions arrays for debugging
    if ($level === 'premium') {
        StripeLogger::log(StripeLogLevel::DEBUG, "Premium permissions array details", [
            'is_array' => is_array($PREMIUM_PERMISSIONS),
            'count' => is_array($PREMIUM_PERMISSIONS) ? count($PREMIUM_PERMISSIONS) : 0,
            'permissions' => is_array($PREMIUM_PERMISSIONS) ? json_encode($PREMIUM_PERMISSIONS) : 'not an array'
        ]);
    }

    $result = null;
    switch ($level) {
        case 'basic':
            $result = $BASIC_PERMISSIONS;
            break;
        case 'advance':
            $result = $ADVANCE_PERMISSIONS;
            break;
        case 'premium':
            $result = $PREMIUM_PERMISSIONS;
            break;
        case 'free':
        default:
            $result = $FREE_PERMISSIONS;
            break;
    }

    // Log the result before returning
    StripeLogger::log(StripeLogLevel::DEBUG, "getPermissionsByLevel returning for level: " . $level, [
        'level' => $level,
        'result_type' => gettype($result),
        'is_array' => is_array($result),
        'count' => is_array($result) ? count($result) : 0
    ]);

    return $result;
}

/**
 * Check if user has a specific permission
 *
 * @param array $userPermissions User's permissions array
 * @param string $permission Permission to check
 * @return bool True if user has permission, false otherwise
 */
function hasPermission($userPermissions, $permission) {
    $result = is_array($userPermissions) && in_array($permission, $userPermissions);

    // Log the permission check
    StripeLogger::log(StripeLogLevel::DEBUG, "hasPermission check", [
        'permission' => $permission,
        'has_permission' => $result,
        'permissions_is_array' => is_array($userPermissions),
        'permissions_count' => is_array($userPermissions) ? count($userPermissions) : 0
    ]);

    return $result;
}

/**
 * Check if user can add more watchlists
 *
 * @param array $userPermissions User's permissions array
 * @param int $currentWatchlistCount Current number of watchlists the user has
 * @return bool True if user can add more watchlists, false otherwise
 */
function canAddMoreWatchlists($userPermissions, $currentWatchlistCount) {
    StripeLogger::log(StripeLogLevel::DEBUG, "canAddMoreWatchlists check", [
        'current_watchlist_count' => $currentWatchlistCount,
        'permissions_is_array' => is_array($userPermissions),
        'permissions_count' => is_array($userPermissions) ? count($userPermissions) : 0
    ]);

    if (hasPermission($userPermissions, 'can_add_unlimited_watchlists')) {
        // Premium plan
        StripeLogger::log(StripeLogLevel::DEBUG, "User has unlimited watchlists permission (Premium plan)");
        return true;
    } else if (hasPermission($userPermissions, 'can_add_10_watchlists') && $currentWatchlistCount < 10) {
        // Advance plan
        StripeLogger::log(StripeLogLevel::DEBUG, "User can add more watchlists (Advance plan)", [
            'limit' => 10,
            'current' => $currentWatchlistCount
        ]);
        return true;
    } else if (hasPermission($userPermissions, 'can_add_3_watchlists') && $currentWatchlistCount < 3) {
        // Basic plan
        StripeLogger::log(StripeLogLevel::DEBUG, "User can add more watchlists (Basic plan)", [
            'limit' => 3,
            'current' => $currentWatchlistCount
        ]);
        return true;
    } else if (hasPermission($userPermissions, 'can_add_1_watchlist') && $currentWatchlistCount < 1) {
        // Free plan
        StripeLogger::log(StripeLogLevel::DEBUG, "User can add more watchlists (Free plan)", [
            'limit' => 1,
            'current' => $currentWatchlistCount
        ]);
        return true;
    }

    StripeLogger::log(StripeLogLevel::DEBUG, "User cannot add more watchlists", [
        'current_watchlist_count' => $currentWatchlistCount
    ]);
    return false;
}

/**
 * Check if user can add more alerts
 *
 * @param array $userPermissions User's permissions array
 * @param int $currentAlertCount Current number of alerts the user has
 * @return bool True if user can add more alerts, false otherwise
 */
function canAddMoreAlerts($userPermissions, $currentAlertCount) {
    StripeLogger::log(StripeLogLevel::DEBUG, "canAddMoreAlerts check", [
        'current_alert_count' => $currentAlertCount,
        'permissions_is_array' => is_array($userPermissions),
        'permissions_count' => is_array($userPermissions) ? count($userPermissions) : 0
    ]);

    if (hasPermission($userPermissions, 'can_add_unlimited_alerts')) {
        // Unlimited alerts (commented out in premium permissions but kept for future use)
        StripeLogger::log(StripeLogLevel::DEBUG, "User has unlimited alerts permission");
        return true;
    } else if (hasPermission($userPermissions, 'can_add_100_alerts') && $currentAlertCount < 100) {
        // Premium plan
        StripeLogger::log(StripeLogLevel::DEBUG, "User can add more alerts (Premium plan)", [
            'limit' => 100,
            'current' => $currentAlertCount
        ]);
        return true;
    } else if (hasPermission($userPermissions, 'can_add_20_alerts') && $currentAlertCount < 20) {
        // Advance plan
        StripeLogger::log(StripeLogLevel::DEBUG, "User can add more alerts (Advance plan)", [
            'limit' => 20,
            'current' => $currentAlertCount
        ]);
        return true;
    } else if (hasPermission($userPermissions, 'can_add_5_alerts') && $currentAlertCount < 5) {
        // Basic plan
        StripeLogger::log(StripeLogLevel::DEBUG, "User can add more alerts (Basic plan)", [
            'limit' => 5,
            'current' => $currentAlertCount
        ]);
        return true;
    }

    // Free plan - no alerts
    StripeLogger::log(StripeLogLevel::DEBUG, "User cannot add more alerts (Free plan)", [
        'current_alert_count' => $currentAlertCount
    ]);
    return false;
}


